In order to use text, voice, or video streaming in ADK, you need to use Gemini models that support the Live API.

Currently, the Live API is available with the following models:

| Model version                              | Availability level |
| ------------------------------------------ | ------------------ |
| gemini-live-2.5-flash                      | Private GA\*       |
| gemini-live-2.5-flash-preview-native-audio | Public preview     |

\* To request access, contact your Google account team representative.

Set SSL_CERT_FILE variable with the following command.

```
export SSL_CERT_FILE=$(python -m certifi)
```