<!DOCTYPE html>
<html>

<head>
    <title><PERSON><PERSON></title>
</head>

<body>

    <div id="log"
        style="border: 1px solid #333; height: 300px; overflow-y: scroll; white-space: pre-wrap; font-family: monospace;">
    </div>

    <input id="input-box" autocomplete="off" placeholder="Type message here...">
    <button onclick="sendMessage()">Send</button>

    <script>
        const userId = Date.now();
        const log = document.getElementById('log');
        const inputBox = document.getElementById('input-box');
        let currentAgentElement = null;

        function appendMessage(text) {
            const messageElement = document.createElement('p');
            messageElement.textContent = text;
            messageElement.style.margin = '5px';
            log.appendChild(messageElement);
            log.scrollTop = log.scrollHeight;
            return messageElement;
        }

        async function sendMessage() {
            const text = inputBox.value;
            if (!text.trim()) return;

            appendMessage(`You: ${text}`);
            inputBox.value = '';
            currentAgentElement = null;

            await fetch(`/send/${userId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ data: text })
            });
        }

        inputBox.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });

        const eventSource = new EventSource(`/events/${userId}`);
        eventSource.onmessage = (event) => {
            const data = JSON.parse(event.data);

            if (data.mime_type === 'text/plain') {
                if (!currentAgentElement) {
                    currentAgentElement = appendMessage('Agent: ');
                }
                currentAgentElement.textContent += data.data;
                log.scrollTop = log.scrollHeight;

            } else if (data.turn_complete) {
                currentAgentElement = null;
            }
        };
    </script>

</body>

</html>