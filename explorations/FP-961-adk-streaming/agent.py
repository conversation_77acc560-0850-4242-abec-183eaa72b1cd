import os

from google.adk.agents import Agent
from google.adk.tools import google_search
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    GOOGLE_API_KEY: str
    GOOGLE_GENAI_USE_VERTEXAI: str

    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8")


settings = Settings()

os.environ["GOOGLE_API_KEY"] = settings.GOOGLE_API_KEY
os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = settings.GOOGLE_GENAI_USE_VERTEXAI

root_agent = Agent(
    name="basic_search_agent",
    model="gemini-2.0-flash-live-001",
    description="Agent to answer questions using Google Search.",
    instruction="You are an expert researcher. You always stick to the facts.",
    tools=[google_search],
)
