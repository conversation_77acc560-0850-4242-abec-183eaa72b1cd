{"cells": [{"cell_type": "code", "execution_count": null, "id": "788b07c3", "metadata": {}, "outputs": [], "source": ["from langfuse import Langfuse, observe, get_client\n", "from pydantic_settings import BaseSettings, SettingsConfigDict\n", "import vertexai\n", "from vertexai.generative_models import (\n", "    GenerationConfig,\n", "    GenerativeModel,\n", "    <PERSON>rmBlockThreshold,\n", "    <PERSON>rm<PERSON>ate<PERSON><PERSON>,\n", "    SafetySetting,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "9e17a759", "metadata": {}, "outputs": [], "source": ["class Settings(BaseSettings):\n", "    langfuse_secret_key: str\n", "    langfuse_public_key: str\n", "    langfuse_host: str\n", "    google_project_id: str\n", "    google_location: str\n", "\n", "    model_config = SettingsConfigDict(env_file=\".env\", env_file_encoding=\"utf-8\")\n", "\n", "\n", "settings = Settings()"]}, {"cell_type": "code", "execution_count": null, "id": "6d860055", "metadata": {}, "outputs": [], "source": ["langfuse = Langfuse(\n", "    secret_key=settings.langfuse_secret_key,\n", "    public_key=settings.langfuse_public_key,\n", "    host=settings.langfuse_host,\n", ")\n", "langfuse.auth_check()"]}, {"cell_type": "code", "execution_count": null, "id": "52d3ed4b", "metadata": {}, "outputs": [], "source": ["prompt = langfuse.get_prompt(name=\"finspector-files-prompt\")"]}, {"cell_type": "code", "execution_count": null, "id": "e627b902", "metadata": {}, "outputs": [], "source": ["print(prompt.version)\n", "print(prompt.labels)\n", "print(prompt.tags)"]}, {"cell_type": "code", "execution_count": null, "id": "8f22dd72", "metadata": {}, "outputs": [], "source": ["print(prompt.prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "d363929e", "metadata": {}, "outputs": [], "source": ["st_rules = \"\"\"\n", "[\n", "  {\n", "    \"rule_id\": 1,\n", "    \"rule_text\": \"Prohibit the use of the words 'guaranteed,' 'protected,' or 'secure' when describing investment performance or returns.\"\n", "  },\n", "  {\n", "    \"rule_id\": 2,\n", "    \"rule_text\": \"Check for presence of 'capital at risk' or similar wording.\"\n", "  },\n", "  {\n", "    \"rule_id\": 3,\n", "    \"rule_text\": \"Only if a specific investment product is discussed, ensure the promotion includes an explanation of all applicable fees.\"\n", "  },\n", "  {\n", "    \"rule_id\": 4,\n", "    \"rule_text\": \"Ensure the promotion states the name of the firm communicating the product.\"\n", "  },\n", "  {\n", "    \"rule_id\": 5,\n", "    \"rule_text\": \"Ensure the promotional material is crafted to responsibly showcase the product, highlighting its potential benefits while transparently disclosing any drawbacks.\"\n", "  },\n", "  {\n", "    \"rule_id\": 6,\n", "    \"rule_text\": \"Identify any potentially misleading information in the promotion.\"\n", "  }\n", "]\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "6cbe43d6", "metadata": {}, "outputs": [], "source": ["compiled_prompt = prompt.compile(st_rules=st_rules)\n", "print(compiled_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "93d2057a", "metadata": {}, "outputs": [], "source": ["safety_settings = [\n", "    SafetySetting(\n", "        category=HarmCategory.HARM_CATEGORY_HATE_SPEECH,\n", "        threshold=HarmBlockThreshold.BLOCK_NONE,\n", "    ),\n", "    SafetySetting(\n", "        category=HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,\n", "        threshold=HarmBlockThreshold.BLOCK_NONE,\n", "    ),\n", "    SafetySetting(\n", "        category=HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,\n", "        threshold=HarmBlockThreshold.BLOCK_NONE,\n", "    ),\n", "    SafetySetting(\n", "        category=HarmCategory.HARM_CATEGORY_HARASSMENT,\n", "        threshold=HarmBlockThreshold.BLOCK_NONE,\n", "    ),\n", "]\n", "\n", "vertexai.init(project=settings.google_project_id, location=settings.google_location)"]}, {"cell_type": "code", "execution_count": null, "id": "fe5e36ee", "metadata": {}, "outputs": [], "source": ["@observe(name=\"gemini-risk-analysis\")\n", "async def call_gemini_async_with_langfuse(\n", "    model_name: str,\n", "    prompt: str,\n", "    top_p: float = 0.2,\n", "    temperature: float = 0.3,\n", ") -> str:\n", "    langfuse_client = get_client()\n", "\n", "    with langfuse_client.start_as_current_generation(\n", "        name=\"gemini-call\",\n", "        model=model_name,\n", "        model_parameters={\n", "            \"temperature\": temperature,\n", "            \"top_p\": top_p,\n", "            \"seed\": 42,\n", "        },\n", "        input=prompt,\n", "    ) as generation:\n", "        generation_config = GenerationConfig(\n", "            temperature=temperature,\n", "            top_p=top_p,\n", "            seed=42,\n", "        )\n", "\n", "        genmodel = GenerativeModel(model_name=model_name)\n", "\n", "        try:\n", "            response = genmodel.generate_content(\n", "                contents=prompt,\n", "                generation_config=generation_config,\n", "                safety_settings=safety_settings,\n", "            )\n", "\n", "            response_text = response.text\n", "            usage_metadata = (\n", "                response.usage_metadata if hasattr(response, \"usage_metadata\") else None\n", "            )\n", "        except Exception as e:\n", "            langfuse_client.update_current_span(level=\"ERROR\", status_message=str(e))\n", "            raise\n", "\n", "        generation.update(\n", "            output=response_text,\n", "            usage_details={\n", "                \"input_tokens\": usage_metadata.prompt_token_count,\n", "                \"output_tokens\": usage_metadata.candidates_token_count,\n", "            },\n", "        )\n", "\n", "    langfuse_client.flush()\n", "\n", "    return response_text"]}, {"cell_type": "code", "execution_count": null, "id": "ebbbfb60", "metadata": {}, "outputs": [], "source": ["context = \"\"\"\n", "Sample Financial Promotion Document\n", "Title: Unlock Your Financial Future with [Product Name]\n", "Introduction:\n", "Discover the unparalleled opportunity to grow your wealth with [Product Name]. Our innovative investment solutions are designed to provide you with exceptional returns, tailored to your financial goals.\n", "Key Features:\n", "● Guaranteed High Returns: Earn up to 15% annual returns with our secure\n", "investment plans.\n", "● Risk-Free Investment: Our strategies ensure your capital remains safe while\n", "yielding substantial profits.\n", "● Exclusive Access: Join an elite group of investors and gain insights from top\n", "financial experts.\n", "Testimonials:\n", "● \"I've doubled my investment in just six months! [Product Name] delivers as\n", "promised.\" – Satisfied Investor\n", "● \"The best decision I've ever made. Financial freedom is now within reach.\" – <PERSON>\n", "Client\n", "Call to Action:\n", "Don't miss out on this exclusive opportunity. Invest in [Product Name] today and secure your prosperous future!\n", "Disclaimer:\n", "Investments carry risks. Past performance is not indicative of future results. Please read the full terms and conditions before investing.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "efa81f0f", "metadata": {}, "outputs": [], "source": ["formatted_prompt = compiled_prompt + \"\\n\\n**The Actual Text:**\\n\\n\" + context"]}, {"cell_type": "code", "execution_count": null, "id": "317235ce", "metadata": {}, "outputs": [], "source": ["result = await call_gemini_async_with_langfuse(\n", "    model_name=\"gemini-2.0-flash\",\n", "    prompt=formatted_prompt,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "finspector-ai-exploration", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}